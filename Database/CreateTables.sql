-- =============================================
-- WTO IDB Submissions Database Tables
-- Manual table creation script
-- =============================================

USE [IdbSubmissions]
GO

-- =============================================
-- Create Users Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Users](
        [Id] [uniqueidentifier] NOT NULL,
        [FirstName] [nvarchar](100) NOT NULL,
        [LastName] [nvarchar](100) NOT NULL,
        [Email] [nvarchar](255) NOT NULL,
        [Age] [int] NOT NULL,
        [IsActive] [bit] NOT NULL,
        [CreatedAt] [datetime2](7) NOT NULL,
        [ModifiedAt] [datetime2](7) NULL,
        [CreatedBy] [nvarchar](255) NULL,
        [ModifiedBy] [nvarchar](255) NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0,
        [DeletedAt] [datetime2](7) NULL,
        [DeletedBy] [nvarchar](255) NULL,
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
    
    PRINT 'Users table created successfully.'
END
ELSE
BEGIN
    PRINT 'Users table already exists.'
END
GO

-- =============================================
-- Create TariffSubmissions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TariffSubmissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TariffSubmissions](
        [Id] [uniqueidentifier] NOT NULL,
        [WTOCountryCode] [nvarchar](3) NOT NULL,
        [Year] [int] NOT NULL,
        [Status] [nvarchar](max) NOT NULL,
        [SubmittedAt] [datetime2](7) NULL,
        [SubmittedBy] [nvarchar](255) NULL,
        [AdditionalContactEmails] [nvarchar](1000) NULL,
        [Origin] [nvarchar](max) NOT NULL,
        [OriginalLanguage] [nvarchar](max) NOT NULL,
        [Currency] [nvarchar](3) NOT NULL,
        [PreferentialTariffs] [bit] NOT NULL,
        [BeneficiaryList] [bit] NOT NULL,
        [OtherDutiesAndCharges] [bit] NOT NULL,
        [DutiesApplicableFrom] [date] NOT NULL,
        [DutiesApplicableTo] [date] NOT NULL,
        [Description] [nvarchar](2000) NULL,
        [CreatedAt] [datetime2](7) NOT NULL,
        [ModifiedAt] [datetime2](7) NULL,
        [CreatedBy] [nvarchar](255) NULL,
        [ModifiedBy] [nvarchar](255) NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0,
        [DeletedAt] [datetime2](7) NULL,
        [DeletedBy] [nvarchar](255) NULL,
        CONSTRAINT [PK_TariffSubmissions] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
    
    PRINT 'TariffSubmissions table created successfully.'
END
ELSE
BEGIN
    PRINT 'TariffSubmissions table already exists.'
END
GO

-- =============================================
-- Create Indexes
-- =============================================

-- Unique index on Users.Email
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = N'IX_Users_Email')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_Email] ON [dbo].[Users]
    (
        [Email] ASC
    )
    
    PRINT 'IX_Users_Email index created successfully.'
END
ELSE
BEGIN
    PRINT 'IX_Users_Email index already exists.'
END
GO

-- Unique composite index on TariffSubmissions (WTOCountryCode, Year) with soft delete filter
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[TariffSubmissions]') AND name = N'IX_TariffSubmissions_WTOCountryCode_Year')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_TariffSubmissions_WTOCountryCode_Year] ON [dbo].[TariffSubmissions]
    (
        [WTOCountryCode] ASC,
        [Year] ASC
    )
    WHERE ([IsDeleted] = 0)
    
    PRINT 'IX_TariffSubmissions_WTOCountryCode_Year index created successfully.'
END
ELSE
BEGIN
    PRINT 'IX_TariffSubmissions_WTOCountryCode_Year index already exists.'
END
GO

PRINT 'Database table creation script completed successfully.'
