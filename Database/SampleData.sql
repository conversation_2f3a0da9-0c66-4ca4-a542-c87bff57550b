-- =============================================
-- Sample Data for WTO IDB Submissions
-- =============================================

USE [IdbSubmissions]
GO

PRINT '=== Inserting Sample Data ==='
PRINT ''

-- Clear existing data (for testing purposes)
DELETE FROM [dbo].[TariffSubmissions]
DELETE FROM [dbo].[Users]

-- =============================================
-- Insert Sample Users
-- =============================================
PRINT 'Inserting sample users...'

INSERT INTO [dbo].[Users] (
    [Id], [FirstName], [LastName], [Email], [Age], [IsActive], 
    [CreatedAt], [CreatedBy], [IsDeleted]
) VALUES 
(NEWID(), 'John', 'Doe', '<EMAIL>', 35, 1, GETUTCDATE(), 'System', 0),
(NEWID(), 'Jane', 'Smith', '<EMAIL>', 42, 1, GETUTCDATE(), 'System', 0),
(NEWID(), 'Pierre', 'Dubois', '<EMAIL>', 38, 1, GETUTCDATE(), 'System', 0),
(NEWID(), 'Hans', 'Mueller', '<EMAIL>', 45, 1, GETUTCDATE(), 'System', 0),
(NEWID(), 'Maria', 'Garcia', '<EMAIL>', 33, 1, GETUTCDATE(), 'System', 0)

PRINT '✓ Sample users inserted'

-- =============================================
-- Insert Sample Tariff Submissions
-- =============================================
PRINT 'Inserting sample tariff submissions...'

-- USA (C840) submissions - matching the screenshot pattern
INSERT INTO [dbo].[TariffSubmissions] (
    [Id], [WTOCountryCode], [Year], [Status], [SubmittedAt], [SubmittedBy],
    [TariffSubmissionOrigin], [OriginalLanguage], [Currency], 
    [PreferentialTariffs], [BeneficiaryList], [OtherDutiesAndCharges],
    [DutiesApplicableFrom], [DutiesApplicableTo], [CreatedAt], [CreatedBy], [IsDeleted]
) VALUES 
-- 2025 - Draft
(NEWID(), 'C840', 2025, 'Draft', NULL, NULL, 'Government', 'English', 'USD', 1, 1, 1, '2025-01-01', '2025-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- 2024 - Draft  
(NEWID(), 'C840', 2024, 'Draft', NULL, NULL, 'Government', 'English', 'USD', 1, 1, 1, '2024-01-01', '2024-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- 2023 - Draft
(NEWID(), 'C840', 2023, 'Draft', NULL, NULL, 'Government', 'English', 'USD', 1, 1, 1, '2023-01-01', '2023-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- 2022 - Draft
(NEWID(), 'C840', 2022, 'Draft', NULL, NULL, 'Government', 'English', 'USD', 1, 1, 1, '2022-01-01', '2022-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- 2019 - Draft
(NEWID(), 'C840', 2019, 'Draft', NULL, NULL, 'Government', 'English', 'USD', 1, 1, 1, '2019-01-01', '2019-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- 2017 - Draft
(NEWID(), 'C840', 2017, 'Draft', NULL, NULL, 'Government', 'English', 'USD', 1, 1, 1, '2017-01-01', '2017-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- Canada (C124) submissions
(NEWID(), 'C124', 2025, 'Draft', NULL, NULL, 'Government', 'English', 'CAD', 1, 1, 1, '2025-01-01', '2025-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C124', 2024, 'Submitted', '2024-03-15 10:30:00', '<EMAIL>', 'Government', 'English', 'CAD', 1, 1, 1, '2024-01-01', '2024-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C124', 2023, 'Finalized', '2023-02-20 14:45:00', '<EMAIL>', 'Government', 'English', 'CAD', 1, 1, 1, '2023-01-01', '2023-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- France (C250) submissions
(NEWID(), 'C250', 2025, 'Draft', NULL, NULL, 'Government', 'French', 'EUR', 1, 1, 1, '2025-01-01', '2025-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C250', 2024, 'Submitted', '2024-04-10 09:15:00', '<EMAIL>', 'Government', 'French', 'EUR', 1, 1, 1, '2024-01-01', '2024-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C250', 2023, 'Accepted', '2023-03-25 16:20:00', '<EMAIL>', 'Government', 'French', 'EUR', 1, 1, 1, '2023-01-01', '2023-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- Germany (C276) submissions
(NEWID(), 'C276', 2025, 'Draft', NULL, NULL, 'Government', 'English', 'EUR', 1, 1, 1, '2025-01-01', '2025-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C276', 2024, 'Draft', NULL, NULL, 'Government', 'English', 'EUR', 1, 1, 1, '2024-01-01', '2024-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C276', 2023, 'Submitted', '2023-05-12 11:30:00', '<EMAIL>', 'Government', 'English', 'EUR', 1, 1, 1, '2023-01-01', '2023-12-31', GETUTCDATE(), '<EMAIL>', 0),

-- Spain (C724) submissions
(NEWID(), 'C724', 2025, 'Draft', NULL, NULL, 'Government', 'Spanish', 'EUR', 1, 1, 1, '2025-01-01', '2025-12-31', GETUTCDATE(), '<EMAIL>', 0),
(NEWID(), 'C724', 2024, 'Finalized', '2024-01-30 13:45:00', '<EMAIL>', 'Government', 'Spanish', 'EUR', 1, 1, 1, '2024-01-01', '2024-12-31', GETUTCDATE(), '<EMAIL>', 0)

PRINT '✓ Sample tariff submissions inserted'

-- =============================================
-- Verify Sample Data
-- =============================================
PRINT ''
PRINT '=== Sample Data Verification ==='

-- Count users
DECLARE @UserCount INT
SELECT @UserCount = COUNT(*) FROM [dbo].[Users] WHERE [IsDeleted] = 0
PRINT 'Total Users: ' + CAST(@UserCount AS NVARCHAR(10))

-- Count tariff submissions
DECLARE @SubmissionCount INT
SELECT @SubmissionCount = COUNT(*) FROM [dbo].[TariffSubmissions] WHERE [IsDeleted] = 0
PRINT 'Total Tariff Submissions: ' + CAST(@SubmissionCount AS NVARCHAR(10))

-- Show submissions by country
PRINT ''
PRINT 'Submissions by Country:'
SELECT 
    [WTOCountryCode],
    COUNT(*) as [SubmissionCount],
    MIN([Year]) as [EarliestYear],
    MAX([Year]) as [LatestYear]
FROM [dbo].[TariffSubmissions] 
WHERE [IsDeleted] = 0
GROUP BY [WTOCountryCode]
ORDER BY [WTOCountryCode]

-- Show submissions by status
PRINT ''
PRINT 'Submissions by Status:'
SELECT 
    [Status],
    COUNT(*) as [Count]
FROM [dbo].[TariffSubmissions] 
WHERE [IsDeleted] = 0
GROUP BY [Status]
ORDER BY [Status]

PRINT ''
PRINT '✓ Sample data insertion completed successfully!'
