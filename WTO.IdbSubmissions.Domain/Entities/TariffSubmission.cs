using System.ComponentModel.DataAnnotations.Schema;
using WTO.IdbSubmissions.Domain.Common;

namespace WTO.IdbSubmissions.Domain.Entities;

[Table("TariffSubmissions")]
public class TariffSubmission : BaseEntity
{
	#region -- Properties --

	public required string WTOCountryCode { get; set; }

	public int Year { get; set; }

	public SubmissionStatus Status { get; set; }

	public DateTime? SubmittedAt { get; set; }

	public string? SubmittedBy { get; set; }

	public string? AdditionalContactEmails { get; set; }

	public TariffSubmissionOrigin Origin { get; set; }

	public WTOLanguage OriginalLanguage { get; set; }

	public required string Currency { get; set; }

	public bool PreferentialTariffs { get; set; }

	public bool BeneficiaryList { get; set; }

	public bool OtherDutiesAndCharges { get; set; }

	public DateOnly DutiesApplicableFrom { get; set; }

	public DateOnly DutiesApplicableTo { get; set; }

	public string? Description { get; set; }

	#endregion
}