using System.Security.Claims;

namespace WTO.IdbSubmissions.Web.Services;

/// <summary>
/// Service for extracting user context information from claims
/// </summary>
public interface IUserContextService
{
    /// <summary>
    /// Extracts the WTO Country Code from user's group claims
    /// </summary>
    /// <param name="user">The user principal</param>
    /// <returns>WTO Country Code if found, null otherwise</returns>
    string? GetWTOCountryCode(ClaimsPrincipal user);

    /// <summary>
    /// Gets all group claims for the user
    /// </summary>
    /// <param name="user">The user principal</param>
    /// <returns>List of group claims</returns>
    List<string> GetUserGroups(ClaimsPrincipal user);

    /// <summary>
    /// Checks if user has a specific group claim
    /// </summary>
    /// <param name="user">The user principal</param>
    /// <param name="groupName">Group name to check</param>
    /// <returns>True if user has the group claim</returns>
    bool HasGroup(ClaimsPrincipal user, string groupName);
}

/// <summary>
/// Implementation of IUserContextService
/// </summary>
public class UserContextService : IUserContextService
{
    private readonly ILogger<UserContextService> _logger;
    private const string GroupsClaimType = "groups";
    private const string CountryGroupPrefix = "COUNTRY_";

    public UserContextService(ILogger<UserContextService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Extracts the WTO Country Code from user's group claims
    /// Looks for groups with "COUNTRY_" prefix and extracts the last 4 characters
    /// Example: "COUNTRY_C804" -> "C804"
    /// </summary>
    /// <param name="user">The user principal</param>
    /// <returns>WTO Country Code if found, null otherwise</returns>
    public string? GetWTOCountryCode(ClaimsPrincipal user)
    {
        if (user?.Identity?.IsAuthenticated != true)
        {
            _logger.LogDebug("User is not authenticated");
            return null;
        }

        var userGroups = GetUserGroups(user);
        
        // Find the first group that starts with COUNTRY_
        var countryGroup = userGroups.FirstOrDefault(g => 
            g.StartsWith(CountryGroupPrefix, StringComparison.OrdinalIgnoreCase));

        if (string.IsNullOrEmpty(countryGroup))
        {
            _logger.LogWarning("User {UserName} does not have any COUNTRY_ group claims", 
                user.Identity?.Name ?? "Unknown");
            return null;
        }

        // Extract the country code (last 4 characters)
        if (countryGroup.Length >= CountryGroupPrefix.Length + 4)
        {
            var countryCode = countryGroup.Substring(countryGroup.Length - 4);
            
            // Validate that it starts with 'C' and has 3 digits
            if (countryCode.StartsWith("C") && countryCode.Length == 4)
            {
                _logger.LogDebug("Extracted WTO Country Code {CountryCode} from group {Group} for user {UserName}", 
                    countryCode, countryGroup, user.Identity?.Name ?? "Unknown");
                return countryCode;
            }
        }

        _logger.LogWarning("Invalid country group format {CountryGroup} for user {UserName}. Expected format: COUNTRY_CXXX", 
            countryGroup, user.Identity?.Name ?? "Unknown");
        return null;
    }

    /// <summary>
    /// Gets all group claims for the user
    /// </summary>
    /// <param name="user">The user principal</param>
    /// <returns>List of group claims</returns>
    public List<string> GetUserGroups(ClaimsPrincipal user)
    {
        if (user?.Identity?.IsAuthenticated != true)
        {
            return new List<string>();
        }

        return user.Claims
            .Where(c => c.Type == GroupsClaimType)
            .Select(c => c.Value)
            .ToList();
    }

    /// <summary>
    /// Checks if user has a specific group claim
    /// </summary>
    /// <param name="user">The user principal</param>
    /// <param name="groupName">Group name to check</param>
    /// <returns>True if user has the group claim</returns>
    public bool HasGroup(ClaimsPrincipal user, string groupName)
    {
        if (user?.Identity?.IsAuthenticated != true || string.IsNullOrWhiteSpace(groupName))
        {
            return false;
        }

        var userGroups = GetUserGroups(user);
        return userGroups.Contains(groupName, StringComparer.OrdinalIgnoreCase);
    }
}
