using WTO.IdbSubmissions.Domain.Common;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;

/// <summary>
/// Data Transfer Object for TariffSubmission status information
/// </summary>
public class TariffSubmissionStatusDto
{
    /// <summary>
    /// Year of the tariff submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// WTO Country Code (ISO 3166-1 alpha-3)
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the submission
    /// </summary>
    public SubmissionStatus Status { get; set; }

    /// <summary>
    /// Indicates if a submission exists for this year and country
    /// </summary>
    public bool HasSubmission { get; set; }

    /// <summary>
    /// Date and time when the submission was submitted (if applicable)
    /// </summary>
    public DateTime? SubmittedAt { get; set; }

    /// <summary>
    /// User who submitted the tariff submission (if applicable)
    /// </summary>
    public string? SubmittedBy { get; set; }

    /// <summary>
    /// Display status text for UI
    /// </summary>
    public string StatusDisplayText => HasSubmission ? Status.ToString().ToUpper() : "NOT SUBMITTED";

    /// <summary>
    /// CSS class for status styling
    /// </summary>
    public string StatusCssClass => HasSubmission 
        ? Status switch
        {
            SubmissionStatus.Draft => "is-warning",
            SubmissionStatus.Submitted => "is-success", 
            SubmissionStatus.Accepted => "is-success",
            SubmissionStatus.Finalized => "is-success",
            _ => "is-light"
        }
        : "is-light";
}
