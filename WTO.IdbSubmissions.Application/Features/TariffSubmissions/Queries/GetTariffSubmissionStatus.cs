using Microsoft.Extensions.Logging;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;

/// <summary>
/// Query to get tariff submission status for a country over the last 10 years
/// </summary>
public class GetTariffSubmissionStatusQuery
{
    /// <summary>
    /// WTO Country Code to get submission status for
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Number of years to look back (default: 10)
    /// </summary>
    public int YearsToLookBack { get; set; } = 10;

    /// <summary>
    /// Current year (default: current year)
    /// </summary>
    public int CurrentYear { get; set; } = DateTime.UtcNow.Year;
}

/// <summary>
/// Response for GetTariffSubmissionStatus query
/// </summary>
public class GetTariffSubmissionStatusResponse
{
    /// <summary>
    /// WTO Country Code
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// List of submission status for each year
    /// </summary>
    public List<TariffSubmissionStatusDto> SubmissionStatuses { get; set; } = new();

    /// <summary>
    /// Total number of years covered
    /// </summary>
    public int TotalYears { get; set; }

    /// <summary>
    /// Number of years with submissions
    /// </summary>
    public int YearsWithSubmissions { get; set; }

    /// <summary>
    /// Number of years without submissions
    /// </summary>
    public int YearsWithoutSubmissions { get; set; }
}

/// <summary>
/// Handler for GetTariffSubmissionStatus query
/// </summary>
public class GetTariffSubmissionStatusHandler
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GetTariffSubmissionStatusHandler> _logger;

    public GetTariffSubmissionStatusHandler(
        IUnitOfWork unitOfWork,
        ILogger<GetTariffSubmissionStatusHandler> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the GetTariffSubmissionStatus query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the submission status response</returns>
    public async Task<Result<GetTariffSubmissionStatusResponse>> HandleAsync(
        GetTariffSubmissionStatusQuery query, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            var validationResult = ValidateQuery(query);
            if (!validationResult.IsSuccess)
            {
                return validationResult;
            }

            _logger.LogInformation("Getting tariff submission status for country {CountryCode} for {Years} years", 
                query.WTOCountryCode, query.YearsToLookBack);

            // Calculate the year range
            var startYear = query.CurrentYear - query.YearsToLookBack + 1;
            var endYear = query.CurrentYear;

            // Get all submissions for the country within the year range
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var submissions = await repository.FindAsync(
                ts => ts.WTOCountryCode == query.WTOCountryCode && 
                      ts.Year >= startYear && 
                      ts.Year <= endYear,
                cancellationToken);

            // Create a dictionary for quick lookup
            var submissionsByYear = submissions.ToDictionary(s => s.Year);

            // Generate status for each year
            var submissionStatuses = new List<TariffSubmissionStatusDto>();
            
            for (int year = endYear; year >= startYear; year--)
            {
                if (submissionsByYear.TryGetValue(year, out var submission))
                {
                    // Submission exists for this year
                    submissionStatuses.Add(new TariffSubmissionStatusDto
                    {
                        Year = year,
                        WTOCountryCode = query.WTOCountryCode,
                        Status = submission.Status,
                        HasSubmission = true,
                        SubmittedAt = submission.SubmittedAt,
                        SubmittedBy = submission.SubmittedBy
                    });
                }
                else
                {
                    // No submission for this year
                    submissionStatuses.Add(new TariffSubmissionStatusDto
                    {
                        Year = year,
                        WTOCountryCode = query.WTOCountryCode,
                        Status = Domain.Common.SubmissionStatus.Draft, // Default status
                        HasSubmission = false,
                        SubmittedAt = null,
                        SubmittedBy = null
                    });
                }
            }

            // Create response
            var response = new GetTariffSubmissionStatusResponse
            {
                WTOCountryCode = query.WTOCountryCode,
                SubmissionStatuses = submissionStatuses,
                TotalYears = query.YearsToLookBack,
                YearsWithSubmissions = submissionStatuses.Count(s => s.HasSubmission),
                YearsWithoutSubmissions = submissionStatuses.Count(s => !s.HasSubmission)
            };

            _logger.LogInformation("Retrieved tariff submission status for country {CountryCode}: {WithSubmissions} with submissions, {WithoutSubmissions} without submissions", 
                query.WTOCountryCode, response.YearsWithSubmissions, response.YearsWithoutSubmissions);

            return Result<GetTariffSubmissionStatusResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get tariff submission status for country {CountryCode}", query.WTOCountryCode);
            return Result<GetTariffSubmissionStatusResponse>.Failure($"Failed to get tariff submission status: {ex.Message}");
        }
    }

    private static Result<GetTariffSubmissionStatusResponse> ValidateQuery(GetTariffSubmissionStatusQuery query)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(query.WTOCountryCode))
        {
            errors.Add("WTO Country Code is required");
        }
        else if (query.WTOCountryCode.Length != 4 || !query.WTOCountryCode.StartsWith("C"))
        {
            errors.Add("WTO Country Code must be 4 characters starting with 'C' (e.g., 'C804')");
        }

        if (query.YearsToLookBack <= 0 || query.YearsToLookBack > 50)
        {
            errors.Add("Years to look back must be between 1 and 50");
        }

        if (query.CurrentYear < 2000 || query.CurrentYear > DateTime.UtcNow.Year + 10)
        {
            errors.Add("Current year must be a valid year");
        }

        if (errors.Any())
        {
            return Result<GetTariffSubmissionStatusResponse>.Failure(errors);
        }

        return Result<GetTariffSubmissionStatusResponse>.Success(new GetTariffSubmissionStatusResponse());
    }
}
